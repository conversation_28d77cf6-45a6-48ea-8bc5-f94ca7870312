# ZK Biometric Cloud Migration Guide

## Overview

This guide will help you migrate your ZK biometric system from Ethernet-only connectivity to cloud-based connectivity, enabling remote access and management of your biometric devices.

## What's New in Cloud Mode

### 🌐 Cloud Connectivity Features
- **Remote Access**: Access your biometric devices from anywhere with internet
- **Real-time Sync**: Automatic synchronization of attendance data to cloud
- **WebSocket Support**: Real-time communication between devices and cloud
- **API Integration**: RESTful APIs for third-party integrations
- **Secure Communication**: Encrypted data transmission with authentication
- **Offline Support**: Queue messages when connection is lost
- **Multi-device Management**: Centralized management of multiple devices

### 🔒 Security Features
- **JWT Authentication**: Secure token-based authentication
- **API Key Management**: Secure API keys for device access
- **Data Encryption**: End-to-end encryption of sensitive data
- **HMAC Signatures**: Request signing for API security
- **Rate Limiting**: Protection against abuse
- **SSL/TLS Support**: Secure communication channels

## Prerequisites

### System Requirements
- Python 3.8 or higher
- Internet connection for cloud features
- Existing ZK biometric device setup
- Administrative access to configure network settings

### Dependencies
Install the new cloud dependencies:
```bash
pip install -r requirements.txt
```

New dependencies include:
- `requests` - HTTP client for API calls
- `websocket-client` - WebSocket communication
- `paho-mqtt` - MQTT messaging (optional)
- `cryptography` - Encryption and security
- `python-dotenv` - Environment variable management
- `schedule` - Task scheduling
- `redis` - Caching and message queuing (optional)
- `celery` - Background task processing (optional)

## Migration Steps

### Step 1: Backup Your Current System

1. **Backup Database**:
   ```bash
   cp vishnorex.db vishnorex_backup_$(date +%Y%m%d).db
   ```

2. **Backup Configuration**:
   ```bash
   cp -r . ../zk_biometric_backup_$(date +%Y%m%d)
   ```

### Step 2: Update Dependencies

1. **Install New Requirements**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Verify Installation**:
   ```bash
   python -c "import cloud_config, cloud_connector, cloud_api; print('Cloud modules installed successfully')"
   ```

### Step 3: Configure Cloud Settings

1. **Run Network Configuration**:
   ```bash
   python network_config.py
   ```
   - Choose option 2 (Cloud) or 3 (Both)
   - Enter your cloud service details
   - Configure device settings

2. **Manual Configuration** (Alternative):
   Create a `.env` file:
   ```env
   CLOUD_API_BASE_URL=https://api.zkcloud.example.com
   CLOUD_WEBSOCKET_URL=wss://ws.zkcloud.example.com
   CLOUD_API_KEY=your_api_key_here
   CLOUD_SECRET_KEY=your_secret_key_here
   CLOUD_ORG_ID=your_organization_id
   CLOUD_USE_SSL=true
   CLOUD_AUTO_SYNC=true
   CLOUD_SYNC_INTERVAL=30
   ```

### Step 4: Update Database Schema

The database will be automatically updated when you start the application. New tables include:
- `cloud_attendance_log` - Cloud attendance records
- `cloud_devices` - Device configurations
- `cloud_sync_log` - Synchronization history
- `api_keys` - API key management

### Step 5: Test Cloud Connectivity

1. **Start the Application**:
   ```bash
   python app.py
   ```

2. **Check Cloud Status**:
   - Navigate to Admin Dashboard
   - Look for "Cloud Status" section
   - Verify cloud connector is running

3. **Test Device Connection**:
   - Go to Device Management
   - Test both Ethernet and Cloud connections
   - Verify device status

## Configuration Options

### Connection Modes

The system now supports three connection modes:

1. **Ethernet Only** (Legacy):
   ```python
   zk_device = ZKBiometricDevice('*************', use_cloud=False)
   ```

2. **Cloud Only**:
   ```python
   zk_device = ZKBiometricDevice(device_id='ZK_001', use_cloud=True)
   ```

3. **Auto-detect** (Recommended):
   ```python
   zk_device = ZKBiometricDevice('*************', device_id='ZK_001')
   # Automatically chooses best connection method
   ```

### Cloud Configuration

Edit `cloud_config.json` or use environment variables:

```json
{
  "config": {
    "cloud_provider": "custom",
    "api_base_url": "https://api.zkcloud.example.com",
    "websocket_url": "wss://ws.zkcloud.example.com",
    "api_key": "encrypted_api_key",
    "secret_key": "encrypted_secret_key",
    "organization_id": "your_org_id",
    "auto_sync": true,
    "sync_interval": 30,
    "use_ssl": true,
    "encryption_enabled": true
  },
  "devices": [
    {
      "device_id": "ZK_001",
      "device_name": "Main Biometric Device",
      "local_ip": "*************",
      "local_port": 4370,
      "cloud_enabled": true,
      "sync_interval": 30
    }
  ]
}
```

## API Endpoints

### Cloud API Routes

The system now includes REST API endpoints:

- `GET /api/cloud/status` - Get cloud connector status
- `GET /api/cloud/devices` - List all devices
- `POST /api/cloud/devices/{id}/sync` - Trigger device sync
- `GET /api/cloud/devices/{id}/users` - Get device users
- `GET /api/cloud/devices/{id}/attendance` - Get attendance records
- `POST /api/cloud/devices/{id}/command` - Send device command
- `POST /api/cloud/attendance/upload` - Upload attendance data

### Authentication

All API requests require authentication:

```bash
curl -H "Authorization: Bearer YOUR_API_KEY" \
     -H "X-Organization-ID: YOUR_ORG_ID" \
     https://api.zkcloud.example.com/api/cloud/status
```

## Troubleshooting

### Common Issues

1. **Cloud Connector Won't Start**:
   - Check API key and organization ID
   - Verify internet connection
   - Check firewall settings
   - Review logs in console

2. **Device Not Connecting**:
   - Verify device is powered on
   - Check local IP address
   - Test Ethernet connection first
   - Ensure device is configured in cloud_config.json

3. **Sync Issues**:
   - Check device status in admin dashboard
   - Verify cloud connector is running
   - Review sync logs
   - Test manual sync

4. **Authentication Errors**:
   - Verify API key is correct
   - Check organization ID
   - Ensure API key hasn't expired
   - Review security logs

### Debug Mode

Enable debug logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Log Files

Check these locations for logs:
- Console output (main application)
- Cloud connector logs
- Device communication logs
- API request/response logs

## Performance Considerations

### Network Requirements
- **Bandwidth**: Minimal (< 1 MB/hour per device)
- **Latency**: < 500ms for real-time features
- **Reliability**: Stable internet connection recommended

### Scaling
- **Devices**: Supports 100+ devices per organization
- **Users**: 10,000+ users per device
- **Concurrent Connections**: 50+ simultaneous connections

### Optimization Tips
1. Adjust sync intervals based on usage patterns
2. Use local caching for frequently accessed data
3. Enable compression for large data transfers
4. Monitor queue sizes and processing times

## Security Best Practices

1. **API Keys**:
   - Use strong, unique API keys
   - Rotate keys regularly
   - Store keys securely (encrypted)
   - Monitor key usage

2. **Network Security**:
   - Use HTTPS/WSS for all communications
   - Enable SSL certificate verification
   - Use VPN for additional security
   - Implement firewall rules

3. **Data Protection**:
   - Enable encryption for sensitive data
   - Use secure password policies
   - Regular security audits
   - Backup encryption keys

## Support and Maintenance

### Regular Maintenance
- Monitor cloud connector status
- Review sync logs weekly
- Update API keys quarterly
- Backup configurations monthly

### Updates
- Check for system updates regularly
- Test updates in staging environment
- Follow semantic versioning
- Maintain backward compatibility

### Getting Help
- Check logs for error messages
- Review this documentation
- Test with minimal configuration
- Contact support with specific error details

## Rollback Plan

If you need to rollback to Ethernet-only mode:

1. **Stop Cloud Connector**:
   ```python
   from cloud_connector import stop_cloud_connector
   stop_cloud_connector()
   ```

2. **Disable Cloud Features**:
   - Set `use_cloud=False` in device initialization
   - Remove cloud configuration
   - Restart application

3. **Restore Backup** (if needed):
   ```bash
   cp vishnorex_backup_YYYYMMDD.db vishnorex.db
   ```

The system maintains full backward compatibility with Ethernet-only mode.
